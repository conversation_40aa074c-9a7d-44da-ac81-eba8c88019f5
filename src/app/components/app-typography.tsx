"use client";

import React from "react";
import { cn } from "@/lib/utils";

export type TypographyVariant =
  | "highlight-1"
  | "highlight-2"
  | "highlight-3"
  | "highlight-4"
  | "highlight-5"
  | "highlight-6"
  | "heading"
  | "heading-strong"
  | "title-3-strong"
  | "title-2-strong"
  | "title-1-strong"
  | "body-4"
  | "body-4-strong"
  | "body-3"
  | "body-3-strong"
  | "body-2"
  | "body-2-strong"
  | "body-1"
  | "body-1-strong"
  | "attribution"
  | "attribution-strong";

export type TypographyElement =
  | "h1"
  | "h2"
  | "h3"
  | "h4"
  | "h5"
  | "h6"
  | "p"
  | "span"
  | "div";

export interface AppTypographyProps {
  variant: TypographyVariant;
  children: React.ReactNode;
  className?: string;
  as?: TypographyElement;
  color?: "primary" | "secondary" | "tertiary" | "quaternary" | "accent";
  style?: React.CSSProperties;
}

const variantToElementMap: Record<TypographyVariant, TypographyElement> = {
  "highlight-1": "h1",
  "highlight-2": "h1",
  "highlight-3": "h1",
  "highlight-4": "h1",
  "highlight-5": "h1",
  "highlight-6": "h1",
  heading: "h2",
  "heading-strong": "h2",
  "title-3-strong": "h3",
  "title-2-strong": "h4",
  "title-1-strong": "h5",
  "body-4": "p",
  "body-4-strong": "p",
  "body-3": "p",
  "body-3-strong": "p",
  "body-2": "p",
  "body-2-strong": "p",
  "body-1": "p",
  "body-1-strong": "p",
  attribution: "span",
  "attribution-strong": "span",
};

const colorClasses = {
  primary: "text-fore-neutral-primary",
  secondary: "text-fore-neutral-secondary",
  tertiary: "text-fore-neutral-tertiary",
  quaternary: "text-fore-neutral-quaternary",
  accent: "text-accent-primary",
};

const sizeClasses = {
  "highlight-1": "text-highlight-1",
  "highlight-2": "text-highlight-2",
  "highlight-3": "text-highlight-3",
  "highlight-4": "text-highlight-4",
  "highlight-5": "text-highlight-5",
  "highlight-6": "text-highlight-6",
  heading: "text-heading",
  "heading-strong": "text-heading-strong",
  "title-3-strong": "text-title-3-strong",
  "title-2-strong": "text-title-2-strong",
  "title-1-strong": "text-title-1-strong",
  "body-4": "text-body-4",
  "body-4-strong": "text-body-4-strong",
  "body-3": "text-body-3",
  "body-3-strong": "text-body-3-strong",
  "body-2": "text-body-2",
  "body-2-strong": "text-body-2-strong",
  "body-1": "text-body-1",
  "body-1-strong": "text-body-1-strong",
  attribution: "text-attribution",
  "attribution-strong": "text-attribution-strong",
};

const AppTypography = ({
  variant,
  children,
  className,
  as,
  color = "primary",
  style,
}: AppTypographyProps) => {
  const Element = as || variantToElementMap[variant];

  const colors = cn(colorClasses[color], className);

  const classes = `${sizeClasses[variant]} ${colors}`;

  return (
    <Element className={classes} style={style}>
      {children}
    </Element>
  );
};

export const H1 = (props: Omit<AppTypographyProps, "variant">) => (
  <AppTypography variant="highlight-1" {...props} />
);

export const H2 = (props: Omit<AppTypographyProps, "variant">) => (
  <AppTypography variant="highlight-2" {...props} />
);

export const H3 = (props: Omit<AppTypographyProps, "variant">) => (
  <AppTypography variant="highlight-3" {...props} />
);

export const H4 = (props: Omit<AppTypographyProps, "variant">) => (
  <AppTypography variant="highlight-4" {...props} />
);

export const H5 = (props: Omit<AppTypographyProps, "variant">) => (
  <AppTypography variant="highlight-5" {...props} />
);

export const H6 = (props: Omit<AppTypographyProps, "variant">) => (
  <AppTypography variant="highlight-6" {...props} />
);

export const Heading = (props: Omit<AppTypographyProps, "variant">) => (
  <AppTypography variant="heading" {...props} />
);

export const HeadingStrong = (props: Omit<AppTypographyProps, "variant">) => (
  <AppTypography variant="heading-strong" {...props} />
);

export const Title3Strong = (props: Omit<AppTypographyProps, "variant">) => (
  <AppTypography variant="title-3-strong" {...props} />
);

export const Title2Strong = (props: Omit<AppTypographyProps, "variant">) => (
  <AppTypography variant="title-2-strong" {...props} />
);

export const Title1Strong = (props: Omit<AppTypographyProps, "variant">) => (
  <AppTypography variant="title-1-strong" {...props} />
);

export const Body4 = (props: Omit<AppTypographyProps, "variant">) => (
  <AppTypography variant="body-4" {...props} />
);

export const Body4Strong = (props: Omit<AppTypographyProps, "variant">) => (
  <AppTypography variant="body-4-strong" {...props} />
);

export const Body3 = (props: Omit<AppTypographyProps, "variant">) => (
  <AppTypography variant="body-3" {...props} />
);

export const Body3Strong = (props: Omit<AppTypographyProps, "variant">) => (
  <AppTypography variant="body-3-strong" {...props} />
);

export const Body2 = (props: Omit<AppTypographyProps, "variant">) => (
  <AppTypography variant="body-2" {...props} />
);

export const Body2Strong = (props: Omit<AppTypographyProps, "variant">) => (
  <AppTypography variant="body-2-strong" {...props} />
);

export const Body1 = (props: Omit<AppTypographyProps, "variant">) => (
  <AppTypography variant="body-1" {...props} />
);

export const Body1Strong = (props: Omit<AppTypographyProps, "variant">) => (
  <AppTypography variant="body-1-strong" {...props} />
);

export const Attribution = (props: Omit<AppTypographyProps, "variant">) => (
  <AppTypography variant="attribution" {...props} />
);

export const AttributionStrong = (
  props: Omit<AppTypographyProps, "variant">
) => <AppTypography variant="attribution-strong" {...props} />;

export default AppTypography;
