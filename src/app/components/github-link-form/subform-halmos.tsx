import { useEffect } from "react";
import { useFormContext } from "react-hook-form";

import { AppInput } from "../app-input";
import { AppSelect } from "../app-select";
import { PreInstallItem } from "./preinstall-item";

export const SubFormHalmos = () => {
  const { register, watch, setValue } = useFormContext();

  const forkMode = watch("forkMode");

  useEffect(() => {}, [forkMode, setValue]);

  return (
    <>
      <div className="mb-6 flex gap-6">
        <AppInput
          className="mb-[8px]"
          label="Tester Contract Name"
          {...register("contract")}
          type="text"
        />

        <AppInput
          className="mb-[8px]"
          label="Tester Function Prefix"
          {...register("halmosPrefix")}
          type="text"
        />
      </div>
      <div className="mb-6 flex gap-6">
        <AppInput
          className="mb-[8px]"
          label="Array Lengths"
          {...register("halmosArray")}
          type="text"
        />

        <AppInput
          className="mb-[8px]"
          label="Loops"
          {...register("halmosLoops")}
          type="text"
        />
      </div>
      <div className="mb-6 flex gap-6">
        <AppSelect
          className="mb-[8px]"
          label="Select verbosity"
          {...register("verbosity")}
          // TODO: standardize where we store and fetch these options
          options={[
            { label: "-v", value: "-v" },
            { label: "-vv", value: "-vv" },
            { label: "-vvv", value: "-vvv" },
            { label: "-vvvv", value: "-vvvv" },
            { label: "-vvvvv", value: "-vvvvv" },
          ]}
          defaultValue="-vvv"
        />

        <PreInstallItem />
      </div>
    </>
  );
};
