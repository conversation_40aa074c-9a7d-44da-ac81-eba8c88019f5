import { useEffect } from "react";
import { useFormContext } from "react-hook-form";

import { AppInput } from "../app-input";
import { AppSelect } from "../app-select";
import { PreInstallItem } from "./preinstall-item";
import { AppCheckbox } from "../app-checkbox";

export const SubFormEchidna = () => {
  const { register, watch, setValue } = useFormContext();

  const forkMode = watch("forkMode");

  useEffect(() => {}, [forkMode, setValue]);

  return (
    <div className="w-[230px]">
      <AppInput
        className="mb-[8px]"
        label="Path to test contract"
        {...register("pathToTester")}
        type="text"
      />

      <AppInput
        className="mb-[8px]"
        label="Echidna config filename"
        {...register("echidnaConfig")}
        type="text"
      />

      <AppInput
        className="mb-[8px]"
        label="Tester Contract Name"
        {...register("contract")}
        type="text"
      />

      <AppInput
        className="mb-[8px]"
        label="Corpus Dir"
        {...register("corpusDir")}
        type="text"
      />

      <AppInput
        className="mb-[8px]"
        label="Test Limit"
        {...register("testLimit")}
        type="text"
      />

      <AppSelect
        className="mb-[8px]"
        label="Select Mode"
        {...register("testMode", { required: "Mode is required" })}
        // TODO: standardize where we store and fetch these options
        options={[
          { label: "Use config", value: "config" },
          { label: "exploration", value: "exploration" },
          { label: "optimization", value: "optimization" },
          { label: "assertion", value: "assertion" },
          { label: "property", value: "property" },
        ]}
      />

      <AppInput
        className="mb-[8px]"
        label="Corpus Re-use Job ID"
        {...register("targetCorpus")}
        type="text"
      />

      <AppSelect
        className="mb-[8px]"
        label="Select Fork Mode"
        {...register("forkMode")}
        // TODO: standardize where we store and fetch these options
        options={[
          { label: "Non-Forked", value: "NONE" },
          { label: "Custom RPC URL", value: "CUSTOM" },
          { label: "Mainnet", value: "MAINNET" },
          { label: "Optimism", value: "OPTIMISM" },
          { label: "Arbitrum", value: "ARBITRUM" },
          { label: "Polygon", value: "POLYGON" },
          { label: "Base", value: "BASE" },
        ]}
      />

      {forkMode && forkMode === "CUSTOM" && (
        <AppInput
          className="mb-[8px]"
          label="RPC URL"
          {...register("rpcUrl")}
          type="text"
          defaultValue=""
        />
      )}

      {forkMode && forkMode !== "NONE" && (
        <AppInput
          className="mb-[8px]"
          label="Fork Block"
          {...register("forkBlock")}
          type="text"
          defaultValue="LATEST"
        />
      )}

      {forkMode && forkMode !== "NONE" && (
        <AppCheckbox
          className="mb-[8px]"
          label="Dynamic Block Replacement"
          {...register("forkReplacement")}
          tooltip="This allows Recon to dynamically replace the fork block and timestamp in your tester. Requires the use of Recon specific tags."
        />
      )}

      <PreInstallItem />
    </div>
  );
};
