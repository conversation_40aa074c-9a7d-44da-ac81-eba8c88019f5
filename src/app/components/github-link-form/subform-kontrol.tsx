import { useEffect } from "react";
import { useFormContext } from "react-hook-form";

import { AppInput } from "../app-input";
import { AppSelectInput } from "../app-select-input";
import { PreInstallItem } from "./preinstall-item";

export const SubFormKontrol = () => {
  const { register, watch, setValue } = useFormContext();

  const forkMode = watch("forkMode");

  useEffect(() => {}, [forkMode, setValue]);

  return (
    <div className="flex gap-6">
      <AppInput
        className="mb-[8px]"
        label="Target Test"
        {...register("kontrolTest")}
        type="text"
      />

      <PreInstallItem />
    </div>
  );
};
