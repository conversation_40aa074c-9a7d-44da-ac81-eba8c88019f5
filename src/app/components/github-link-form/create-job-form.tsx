import { <PERSON><PERSON><PERSON><PERSON>, useField<PERSON>rray, useForm } from "react-hook-form";
import { FaCheckCircle } from "react-icons/fa";

import { ENV_TYPE } from "@/app/app.constants";
import { useGetRecipes } from "@/app/services/recipes.hook";

import { AppButton } from "../app-button";
import { AppInput } from "../app-input";
import { AppSpinner } from "../app-spinner";
import { JobTypeFuzzer } from "../job-type-fuzzer";
import { SubFormEchidna } from "./subform-echidna";
import { SubFormFoundry } from "./subform-foundry";
import { SubFormMedusa } from "./subform-medusa";
import { SubFormHalmos } from "./subform-halmos";
import { SubFormKontrol } from "./subform-kontrol";
import { useEffect } from "react";

import type { PreparedDynamicReplacementContract } from "@/app/(app)/dashboard/dynamic-replacement/page";

type GitHubLinkInputProps = {
  onSubmit(e: any): void;
  title: string;
  submitLabel?: string;
  hideEnv?: boolean;
  hidePresets?: boolean;
  env?: ENV_TYPE;
  setEnv?(env: ENV_TYPE): void;
  jobId?: number | null;
  dynamicReplacement?: boolean;
  buildHandler?: boolean;
  setRecipeId?: (e: string) => void;
};

export type GitHubLinkFormValues = {
  // UX
  githubURL: string;

  // Basic
  orgName: string;
  repoName: string;
  ref: string;
  directory: string;
  customOut: string;

  //TODO 0xsi
  label: string;

  // Preprocess / Compatibility
  preprocess: string;
  targetCorpus: string;

  // Medusa
  medusaConfig: string;
  timeout: string;

  // Echidna
  pathToTester: string;
  echidnaConfig: string;
  forkBlock: string;
  forkMode: string;
  contract: string;
  corpusDir: string;
  rpcUrl: string;
  testLimit: string;
  testMode: string;
  forkReplacement: string;

  // Foundry
  runs: string;
  seed: string;
  verbosity: string;
  testCommand: string;
  // Note that `contract`, `forkBlock`, `forkMode` and `rpcUrl` is used by Echidna too

  // Halmos
  // Halmos also uses the shared `contract` and `verbosity` fields
  halmosPrefix: string;
  halmosArray: string;
  halmosLoops: string;

  testTarget: string;
  // Note that `contract`, `forkBlock` and `forkMode` is used by Echidna too

  // Kontrol
  kontrolTest: string;

  // Dynamic replacement
  prepareContracts: PreparedDynamicReplacementContract[]; // ?  TODO 0XSI
  fields: DynamicReplacementFieldsGroup[];
};

export type DynamicReplacementFieldsGroup = {
  variableName: string;
  interface: string;
  value: string;
};

export function CreateJobForm({
  onSubmit,
  title,
  submitLabel = "Start Job",
  hideEnv = false,
  hidePresets = false,
  env,
  setEnv,
  jobId,
  dynamicReplacement,
  buildHandler,
  setRecipeId,
}: GitHubLinkInputProps) {
  const {
    register,
    handleSubmit,
    setValue,
    setError,
    watch,
    formState: { errors, isSubmitting },
  } = useForm<GitHubLinkFormValues>({
    defaultValues: {
      timeout: "3600",
      testLimit: "100000",
    },
  });
  const { data: recipes } = useGetRecipes();

  const orgName = watch("orgName");
  const repoName = watch("repoName");
  const ref = watch("ref");

  const methods = useForm<GitHubLinkFormValues>({
    defaultValues: {
      fields: [{ variableName: "", interface: "", value: "" }],
    },
  });
  const { control } = methods;

  const { fields, append, remove } = useFieldArray({
    control,
    name: "fields",
  });

  const watchedFields = watch("fields");

  useEffect(() => {
    // Filter out incomplete field groups
    if (!watchedFields) return;

    const validFields = watchedFields.filter(
      (field) =>
        field.variableName.trim() &&
        field.interface.trim() &&
        field.value.trim()
    );

    const prepContract = validFields.map((field) => ({
      target: `${field.variableName} = ${field.interface}`,
      replacement: `${field.variableName} = ${field.interface}(${field.value});`,
      endOfTargetMarker: "[^;]*",
      targetContract: "Setup.sol",
    }));
    setValue("prepareContracts", prepContract);

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [watchedFields, methods.getValues()]);

  function applyDefault(defaults) {
    setRecipeId(defaults.id);
    console.log("applyDefault defaults", defaults);
    // NOTE: Defaults can be fairly complex
    // Let's try:
    const keys = Object.keys(defaults);
    const filtered = keys.filter(
      (key) => key != "fuzzerArgs" && key != "fuzzer"
    );
    // NOTE: Add them this way
    if (defaults?.fuzzer) {
      setEnv(defaults.fuzzer);
    }

    // Update all keys except Fuzzer and FuzzerArgs
    for (let i = 0; i < filtered.length; i++) {
      // @ts-expect-error we don't care
      setValue(String(filtered[i]), defaults[filtered[i]]);
    }

    if (defaults?.fuzzerArgs) {
      setValue("pathToTester", defaults.fuzzerArgs.pathToTester);
      setValue("testLimit", defaults.fuzzerArgs.testLimit);
      setValue("echidnaConfig", defaults.fuzzerArgs.config);
      setValue("contract", defaults.fuzzerArgs.contract);
      setValue("corpusDir", defaults.fuzzerArgs.corpusDir);
      setValue("forkBlock", defaults.fuzzerArgs.forkBlock);
      setValue("forkReplacement", defaults.fuzzerArgs.forkReplacement);
      setValue("forkMode", defaults.fuzzerArgs.forkMode);
      setValue("rpcUrl", defaults.fuzzerArgs.rpcUrl);
      setValue("targetCorpus", defaults.fuzzerArgs.targetCorpus);
      setValue("testMode", defaults.fuzzerArgs.testMode);

      setValue("timeout", defaults.fuzzerArgs.timeout);
      setValue("medusaConfig", defaults.fuzzerArgs.config);

      setValue("runs", defaults.fuzzerArgs.runs);
      setValue("testCommand", defaults.fuzzerArgs.testCommand);
      setValue("testTarget", defaults.fuzzerArgs.testTarget);
      setValue("seed", defaults.fuzzerArgs.seed);
      setValue("verbosity", defaults.fuzzerArgs.verbosity);

      setValue("halmosPrefix", defaults.fuzzerArgs.halmosPrefix);
      setValue("halmosArray", defaults.fuzzerArgs.halmosArray);
      setValue("halmosLoops", defaults.fuzzerArgs.halmosLoops);

      setValue("kontrolTest", defaults.fuzzerArgs.kontrolTest);

      setValue("label", defaults.fuzzerArgs.label);
    }
  }

  const parseURI = (inputValue) => {
    console.log("input value", inputValue);
    const success = /^(https?:\/\/)?(www\.)?github\.com\/.+\/.+(\.git)?$/.test(
      inputValue
    );

    if (!success) {
      setError("githubURL", {
        message: "Invalid GitHub URL",
      });
      return;
    } else {
      setError("githubURL", null);
    }

    const ghLink = inputValue.endsWith("/")
      ? inputValue.slice(0, -1)
      : inputValue;
    const uriParts = ghLink
      .replace("https://", "")
      .replace("http://", "")
      .split("/");

    if (uriParts.length >= 3) {
      const orgName = uriParts[1];
      const repoName = uriParts[2];
      let ref = "main";

      if (uriParts.length > 5 && uriParts[3] === "tree") {
        ref = uriParts.slice(4).join("/");
      } else if (uriParts.length === 5 && uriParts[3] === "tree") {
        ref = uriParts[4];
      }

      // Set the values to the form
      setValue("orgName", orgName);
      setValue("repoName", repoName);
      setValue("ref", ref);
    }
  };

  const githubUrlRegister = register("githubURL");

  return (
    <FormProvider
      {...({
        register,
        handleSubmit,
        setValue,
        setError,
        watch,
        errors,
        isSubmitting,
      } as any)}
    >
      <form onSubmit={handleSubmit(onSubmit)}>
        <h3 className="mb-[22px] text-[28px] leading-[33px] text-textPrimary">
          {title}
        </h3>

        {!hidePresets && recipes && (
          <div className="my-[20px] flex flex-wrap gap-[20px]">
            {recipes.map((recipe) => (
              <AppButton
                onClick={() => applyDefault(recipe)}
                className=" px-[14px] py-[9px] leading-[21px]"
                key={recipe.displayName}
              >
                Use Default `{recipe.displayName}`
              </AppButton>
            ))}
          </div>
        )}

        {!hideEnv && env && (
          <div className="mb-6 w-full">
            <JobTypeFuzzer value={env} onChange={(value) => setEnv(value)} />
          </div>
        )}

        <div className="flex flex-col gap-6 self-stretch">
          <div className="flex flex-col gap-6 self-stretch">
            {!buildHandler && (
              <AppInput
                label="Job Label"
                {...register("label")}
                type="text"
                defaultValue=""
              />
            )}

            <AppInput
              {...githubUrlRegister}
              onChange={(e) => {
                githubUrlRegister.onChange(e);
                parseURI(e.target.value);
              }}
              type="text"
              label="GitHub Repo URL"
              placeholder="Enter GitHub Repo URL"
              error={errors.githubURL?.message}
            />
          </div>

          <div className="h-px w-full bg-white/10" />

          <div className="flex flex-col gap-4 self-stretch">
            <h3 className="text-xl font-bold leading-[1.3] text-[#F5F5F5]">
              Or specify the organization, repository and branch directly
            </h3>

            <div className="flex w-full flex-col gap-6 self-stretch">
              <div className="flex gap-6">
                <AppInput
                  label="Organization"
                  {...register("orgName")}
                  type="text"
                />
                <AppInput
                  {...register("repoName")}
                  type="text"
                  label="Repo"
                />
              </div>

              <div className="flex flex-row gap-6 self-stretch">
                <AppInput {...register("ref")} type="text" label="Branch " />
                <AppInput
                  {...register("directory")}
                  type="text"
                  label="Directory"
                />
              </div>
            </div>

            {!env && (
              <AppInput
                {...register("customOut")}
                type="text"
                label="Custom output folder"
              />
            )}
            {dynamicReplacement && (
              <h3 className="mb-4 text-base leading-[19px] text-textPrimary">
                Dynamic replacement
              </h3>
            )}

            {dynamicReplacement &&
              fields.map((field, index) => (
                <div
                  key={field.id}
                  className="relative mb-4 rounded border-b-orange-50"
                >
                  <AppInput
                    className="mb-2"
                    label="Variable Name"
                    {...register(`fields.${index}.variableName` as const)}
                    type="text"
                    defaultValue={field.variableName}
                  />
                  <AppInput
                    className="mb-2"
                    label="Interface"
                    {...register(`fields.${index}.interface` as const)}
                    type="text"
                    defaultValue={field.interface}
                  />
                  <AppInput
                    className="mb-2"
                    label="Value"
                    {...register(`fields.${index}.value` as const)}
                    type="text"
                    defaultValue={field.value}
                  />
                  <button
                    type="button"
                    onClick={() => remove(index)}
                    className="absolute bottom-1 right-1 size-[18px] text-white underline"
                    title="Delete Field Group"
                  >
                    Remove
                  </button>
                </div>
              ))}

            {dynamicReplacement && (
              <AppButton
                type="button"
                onClick={() =>
                  append({ variableName: "", interface: "", value: "" })
                }
                className="mt-4 rounded p-2 text-white transition-colors"
              >
                Add More Fields
              </AppButton>
            )}
          </div>

          <div className="h-px w-full bg-white/10" />

          {!hideEnv && env && (
            <div className="flex flex-col gap-4 self-stretch">
              <h3 className="text-xl font-bold leading-[1.3] text-[#F5F5F5]">
                Configure custom parameters for {env.toLowerCase()}:
              </h3>

              <div className="flex flex-col gap-8 self-stretch">
                {env === ENV_TYPE.MEDUSA ? (
                  <SubFormMedusa />
                ) : env === ENV_TYPE.ECHIDNA ? (
                  <SubFormEchidna />
                ) : env === ENV_TYPE.FOUNDRY ? (
                  <SubFormFoundry />
                ) : env === ENV_TYPE.HALMOS ? (
                  <SubFormHalmos />
                ) : (
                  <SubFormKontrol />
                )}
              </div>
            </div>
          )}
          <div className="h-px w-full bg-white/10" />

          <AppButton
            type="submit"
            disabled={isSubmitting}
            size="lg"
            fullWidth
            className="flex flex-row items-center justify-center gap-1 self-stretch rounded-lg px-3 py-2"
          >
            {isSubmitting ? <AppSpinner /> : submitLabel}
          </AppButton>
          {!!jobId && (
            <div className="flex flex-col gap-4 self-stretch">
              <h3 className="text-xl font-bold leading-[1.3] text-[#F5F5F5]">
                Job Details:
              </h3>
              <div className="flex items-center justify-center gap-3 rounded-lg bg-success p-4 text-lg leading-[21px] text-textPrimary">
                <FaCheckCircle className="size-5 text-textPrimary" />
                Job has been successfully created
              </div>
              <p className="mb-4 text-base leading-[18px] text-textPrimary">
                ID: {jobId}
              </p>

              {[
                `Org: ${orgName ?? ""}`,
                `Repo: ${repoName ?? ""}`,
                `Branch: ${ref ?? ""}`,
                `Directory: ${watch("directory") ?? ""}`,
                `Custom Out: ${watch("customOut") ?? ""}`,
              ]
                .filter(Boolean)
                .map((line) => (
                  <p
                    key={line}
                    className="mb-1 w-full border-b border-divider pb-4 text-base leading-[18px] text-textSecondary"
                  >
                    {line}
                  </p>
                ))}
            </div>
          )}
        </div>
      </form>
    </FormProvider>
  );
}
