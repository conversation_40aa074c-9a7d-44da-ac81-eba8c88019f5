import { useFormContext } from "react-hook-form";

import { AppInput } from "../app-input";
import { PreInstallItem } from "./preinstall-item";

export const SubFormMedusa = () => {
  const { register } = useFormContext();

  return (
    <div className="flex flex-col gap-8 self-stretch">
      <div className="flex flex-row gap-6 self-stretch">
        <AppInput
          label="Medusa config filename"
          {...register("medusaConfig")}
          type="text"
        />
        <AppInput
          label="Test time limit"
          {...register("timeout")}
          type="text"
        />
      </div>

      <div className="flex flex-row gap-6 self-stretch">
        <AppInput
          label="Corpus re-use job ID"
          {...register("targetCorpus")}
          type="text"
        />
        <PreInstallItem />
      </div>
    </div>
  );
};
