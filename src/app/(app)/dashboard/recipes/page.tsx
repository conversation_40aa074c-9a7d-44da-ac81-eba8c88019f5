"use client";

import { AppListCheckItem } from "@/app/components/app-list-check-item";

import { AllRecipes } from "./AllRecipes";
import { CreateUpdateRecipeForm } from "./CreateUpdateRecipe";

export default function Campaigns() {
  const renderListItem = (text) => {
    return <AppListCheckItem text={text} key={text} />;
  };

  return (
    <div className="flex flex-col gap-6 px-[200px] py-6">
      <div className="flex flex-col gap-1 self-stretch">
        <h1 className="text-[54px] font-bold leading-[1.185] text-[#DFDBFA]">
          Recipes
        </h1>
        <div className="flex flex-col gap-4 text-[15px] leading-[18px] text-textSecondary">
          <p className="text-[20px] leading-[25px]">
            Recipes are Job Configurations.
            <br />
            They can be used as:
          </p>

          <ul className="text-[16px] leading-[26px]">
            {["Presets in the Jobs Page", "Template for Campaigns"].map(
              renderListItem
            )}
          </ul>

          <p>
            Recipes appear as buttons in the Jobs Page
            <br />
            And can be used with Campaigns to automatically run on PR or Commit
          </p>
        </div>
      </div>

      <CreateUpdateRecipeForm />
      <AllRecipes />
    </div>
  );
}
